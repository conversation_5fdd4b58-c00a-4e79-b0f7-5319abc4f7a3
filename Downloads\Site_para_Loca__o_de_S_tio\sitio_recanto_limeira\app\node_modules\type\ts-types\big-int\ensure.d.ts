import { EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureBigInt(value: any, options?: EnsureBaseOptions): bigint;
declare function ensureBigInt(value: any, options?: EnsureBaseOptions & EnsureIsOptional): bigint | null;
declare function ensureBigInt(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<bigint>): bigint;

export default ensureBigInt;
