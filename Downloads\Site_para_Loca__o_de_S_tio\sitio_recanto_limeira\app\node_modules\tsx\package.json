{"name": "tsx", "version": "3.12.10", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "license": "MIT", "repository": "esbuild-kit/tsx", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "type": "module", "files": ["dist"], "exports": {"./package.json": "./package.json", ".": "./dist/loader.js", "./cli": "./dist/cli.js", "./suppress-warnings": "./dist/suppress-warnings.cjs", "./preflight": "./dist/preflight.cjs", "./repl": "./dist/repl.js"}, "bin": "./dist/cli.js", "dependencies": {"@esbuild-kit/cjs-loader": "^2.4.2", "@esbuild-kit/core-utils": "^3.3.0", "@esbuild-kit/esm-loader": "^2.6.3"}, "optionalDependencies": {"fsevents": "~2.3.2"}}