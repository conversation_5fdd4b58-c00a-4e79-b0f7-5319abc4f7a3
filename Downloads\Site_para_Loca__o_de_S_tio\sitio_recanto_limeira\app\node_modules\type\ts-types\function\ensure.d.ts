import { EnsureFunction, EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureFunction(value: any, options?: EnsureBaseOptions): EnsureFunction;
declare function ensureFunction(value: any, options?: EnsureBaseOptions & EnsureIsOptional): EnsureFunction | null;
declare function ensureFunction(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<EnsureFunction>): EnsureFunction;

export default ensureFunction;
